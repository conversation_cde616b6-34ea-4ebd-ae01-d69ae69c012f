from odoo import models, fields, api

class JobDescription(models.Model):
    _name = 'job.description'
    _description = 'Job Description'

    name = fields.Char(string='Position Title', required=True)
    job_grade = fields.Char(string='Job Grade')
    company_id = fields.Many2one('res.company', string='Company')
    department_id = fields.Many2one('hr.department', string='Department')
    division = fields.Char(string='Division')
    section = fields.Char(string='Section / Unit')

    direct_report = fields.Char(string='Directly Reports To')
    direct_supervise = fields.Text(string='Directly Supervises')
    functional_report = fields.Text(string='Functionally Reports To')
    functional_supervise = fields.Text(string='Functionally Supervises')

    direct_count = fields.Integer(string='Direct Supervised')
    indirect_count = fields.Integer(string='Indirect Supervised')
    total_supervised = fields.Integer(string='Total Supervised', compute='_compute_total')

    core_responsibilities = fields.Text(string='Core Responsibilities')
    functional_responsibilities = fields.Text(string='Functional Responsibilities')

    qualifications = fields.Text(string='Qualifications')
    experience = fields.Text(string='Working Experience')
    skills = fields.Text(string='Special Skills Required')

    authority_limit_staff = fields.Selection([
        ('operational', 'Budgeted Operational/Support'),
        ('executive', 'Budgeted Executive/Officer'),
        ('managerial', 'Budgeted Managerial')
    ], string='Staff Hiring Authority')

    contractual_single = fields.Float(string='Contractual Authority (Single)')
    contractual_joint = fields.Float(string='Contractual Authority (Joint)')

    petty_cash = fields.Float(string='Petty Cash Limit')
    revenue_expenditure = fields.Float(string='Revenue Expenditure')
    capital_expenditure = fields.Float(string='Capital Expenditure')

    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected')
    ], string='Status', default='draft', tracking=True)

    @api.depends('direct_count', 'indirect_count')
    def _compute_total(self):
        for rec in self:
            rec.total_supervised = (rec.direct_count or 0) + (rec.indirect_count or 0)

    def action_submit(self):
        for rec in self:
            rec.state = 'submitted'

    def action_approve(self):
        for rec in self:
            rec.state = 'approved'

    def action_reject(self):
        for rec in self:
            rec.state = 'rejected'
