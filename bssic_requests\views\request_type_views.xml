<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Request Type Form View -->
    <record id="view_bssic_request_type_form" model="ir.ui.view">
        <field name="name">bssic.request.type.form</field>
        <field name="model">bssic.request.type</field>
        <field name="arch" type="xml">
            <form string="Request Type">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Request Type Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="show_password_fields"/>
                            <field name="show_usb_fields"/>
                            <field name="show_extension_fields"/>
                            <field name="show_permission_fields"/>
                            <field name="show_email_fields"/>
                            <!-- field name="show_authorization_delegation_fields" removed -->
                            <field name="show_free_entry_fields"/>
                        </group>
                    </group>
                    <group>
                        <field name="description" placeholder="Description..."/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Request Type Tree View -->
    <record id="view_bssic_request_type_tree" model="ir.ui.view">
        <field name="name">bssic.request.type.tree</field>
        <field name="model">bssic.request.type</field>
        <field name="arch" type="xml">
            <tree string="Request Types">
                <field name="name"/>
                <field name="code"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <!-- Request Type Search View -->
    <record id="view_bssic_request_type_search" model="ir.ui.view">
        <field name="name">bssic.request.type.search</field>
        <field name="model">bssic.request.type</field>
        <field name="arch" type="xml">
            <search string="Search Request Types">
                <field name="name"/>
                <field name="code"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>
</odoo>