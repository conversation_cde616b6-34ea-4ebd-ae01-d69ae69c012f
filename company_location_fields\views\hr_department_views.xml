<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Action to show last used numbers -->
        <record id="action_show_last_used_numbers" model="ir.actions.server">
            <field name="name">Show Last Used Numbers</field>
            <field name="model_id" ref="hr.model_hr_department"/>
            <field name="binding_model_id" ref="hr.model_hr_department"/>
            <field name="binding_view_types">form</field>
            <field name="state">code</field>
            <field name="code">
def run_action(self):
    last_numbers = self.get_last_used_numbers(self.env.context.get('default_company_id'))
    
    # Create message to display
    message = """
    &lt;div style="font-size: 14px; margin-bottom: 10px;"&gt;
        &lt;strong&gt;Last Used Numbers:&lt;/strong&gt;
    &lt;/div&gt;
    &lt;ul&gt;
    """
    
    if last_numbers.get('division_number'):
        message += """
        &lt;li&gt;&lt;strong&gt;Division Number:&lt;/strong&gt; %s&lt;/li&gt;
        """ % last_numbers['division_number']
    else:
        message += """
        &lt;li&gt;&lt;strong&gt;Division Number:&lt;/strong&gt; No division number used yet&lt;/li&gt;
        """
        
    if last_numbers.get('department_number'):
        message += """
        &lt;li&gt;&lt;strong&gt;Department Number:&lt;/strong&gt; %s&lt;/li&gt;
        """ % last_numbers['department_number']
    else:
        message += """
        &lt;li&gt;&lt;strong&gt;Department Number:&lt;/strong&gt; No department number used yet&lt;/li&gt;
        """
        
    if last_numbers.get('unit_number'):
        message += """
        &lt;li&gt;&lt;strong&gt;Unit Number:&lt;/strong&gt; %s&lt;/li&gt;
        """ % last_numbers['unit_number']
    else:
        message += """
        &lt;li&gt;&lt;strong&gt;Unit Number:&lt;/strong&gt; No unit number used yet&lt;/li&gt;
        """
    
    message += """
    &lt;/ul&gt;
    &lt;div style="font-size: 12px; margin-top: 10px; color: #888;"&gt;
        Note: Numbers must be unique within the same company.
    &lt;/div&gt;
    """
    
    # Show the message
    return {
        'type': 'ir.actions.client',
        'tag': 'display_notification',
        'params': {
            'title': 'Last Used Numbers',
            'message': message,
            'sticky': True,
            'type': 'info',
            'next': {'type': 'ir.actions.act_window_close'},
        }
    }

action = run_action(model)
            </field>
        </record>
        
        <!-- Inherit Department Form View -->
        <record id="view_department_form_inherit_location" model="ir.ui.view">
            <field name="name">hr.department.form.inherit.location</field>
            <field name="model">hr.department</field>
            <field name="inherit_id" ref="hr.view_department_form"/>
            <field name="arch" type="xml">
                <!-- Add fields after name field -->
                <xpath expr="//field[@name='name']" position="after">
                    <field name="sector_code" placeholder="e.g., SEC01"/>
                    <field name="division_number" placeholder="e.g., 01"/>
                    <field name="department_number" placeholder="e.g., 001"/>
                    <field name="unit_number" placeholder="e.g., U01"/>
                </xpath>
                
                <!-- Add full code field after the form group -->
                <xpath expr="//group" position="after">
                    <group>
                        <field name="full_code" readonly="1" 
                               attrs="{'invisible': [('full_code', '=', False)]}"
                               style="font-weight: bold; font-size: 1.2em;"/>
                    </group>
                </xpath>
            </field>
        </record>

        <!-- Inherit Department Tree View -->
        <record id="view_department_tree_inherit_location" model="ir.ui.view">
            <field name="name">hr.department.tree.inherit.location</field>
            <field name="model">hr.department</field>
            <field name="inherit_id" ref="hr.view_department_tree"/>
            <field name="arch" type="xml">
                <!-- Add fields after display_name field -->
                <xpath expr="//field[@name='display_name']" position="after">
                    <field name="full_code"/>
                    <field name="sector_code"/>
                    <field name="division_number"/>
                    <field name="department_number"/>
                    <field name="unit_number"/>
                </xpath>
            </field>
        </record>

        <!-- Inherit Department Search View -->
        <record id="view_department_filter_inherit_location" model="ir.ui.view">
            <field name="name">hr.department.search.inherit.location</field>
            <field name="model">hr.department</field>
            <field name="inherit_id" ref="hr.view_department_filter"/>
            <field name="arch" type="xml">
                <!-- Add fields to search -->
                <xpath expr="//field[@name='name']" position="after">
                    <field name="full_code"/>
                    <field name="sector_code"/>
                    <field name="division_number"/>
                    <field name="department_number"/>
                    <field name="unit_number"/>
                </xpath>
                
                <!-- Add group by options -->
                <xpath expr="//filter[@name='inactive']" position="after">
                    <group expand="0" string="Group By">
                        <filter string="Sector Code" name="group_by_sector_code" 
                                context="{'group_by': 'sector_code'}"/>
                        <filter string="Division Number" name="group_by_division_number" 
                                context="{'group_by': 'division_number'}"/>
                    </group>
                </xpath>
            </field>
        </record>
    </data>
</odoo>