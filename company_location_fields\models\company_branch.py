# -*- coding: utf-8 -*-

from odoo import models, fields, api


class CompanyBranch(models.Model):
    _name = 'company.branch'
    _description = 'Company Branch'
    _order = 'sequence, name'
    _rec_name = 'name'

    name = fields.Char(
        string='Branch Name',
        required=True,
        translate=True,
        help='Name of the branch'
    )
    code = fields.Char(
        string='Branch Code',
        size=10,
        help='Short code for the branch'
    )
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Used to order branches'
    )
    active = fields.Boolean(
        string='Active',
        default=True,
        help='If unchecked, it will allow you to hide the branch without removing it.'
    )
    description = fields.Text(
        string='Description',
        translate=True,
        help='Additional information about the branch'
    )
    address = fields.Text(
        string='Address',
        translate=True,
        help='Address of the branch'
    )
    phone = fields.Char(
        string='Phone',
        help='Phone number of the branch'
    )
    email = fields.Char(
        string='Email',
        help='Email address of the branch'
    )
    manager_name = fields.Char(
        string='Branch Manager',
        translate=True,
        help='Name of the branch manager'
    )
    branch_type = fields.Selection([
        ('main', 'Main Branch'),
        ('sub', 'Sub Branch'),
        ('office', 'Office'),
        ('warehouse', 'Warehouse'),
        ('other', 'Other')
    ], string='Branch Type', default='sub', help='Type of the branch')
    
    general_management_id = fields.Many2one(
        'general.management',
        string='General Management',
        help='General Management this branch belongs to',
        domain=[('active', '=', True)]
    )
    
    company_country_id = fields.Many2one(
        'company.country',
        string='Country',
        related='general_management_id.country_id',
        store=True,
        readonly=True,
        help='Country this branch belongs to (derived from General Management)'
    )
    
    company_ids = fields.One2many(
        'res.company',
        'company_branch_id',
        string='Companies',
        help='Companies using this branch'
    )

    @api.depends('name', 'code', 'branch_type')
    def name_get(self):
        result = []
        for record in self:
            if record.code:
                name = record.code
            else:
                name = record.name
            result.append((record.id, name))
        return result

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        args = args or []
        if name:
            # Search by name or code
            domain = ['|', ('name', operator, name), ('code', operator, name)]
            records = self.search(domain + args, limit=limit)
            return records.name_get()
        return super(CompanyBranch, self).name_search(name, args, operator, limit)
        
    @api.onchange('general_management_id')
    def _onchange_general_management_id(self):
        """Update domain and related fields when general management changes"""
        if self.general_management_id:
            return {'domain': {'general_management_id': [('active', '=', True)]}}
        return {}

    @api.depends('company_ids')
    def _compute_companies_count(self):
        for record in self:
            record.companies_count = len(record.company_ids)

    companies_count = fields.Integer(
        string='Companies Count',
        compute='_compute_companies_count',
        store=True,
        help='Number of companies using this branch'
    )
