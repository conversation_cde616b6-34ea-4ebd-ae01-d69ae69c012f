<odoo>
    <data>
        <record id="view_form_job_description" model="ir.ui.view">
            <field name="name">job.description.form</field>
            <field name="model">job.description</field>
            <field name="arch" type="xml">
                <form string="Job Description">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="job_grade"/>
                            <field name="company_id"/>
                            <field name="department_id"/>
                            <field name="division"/>
                            <field name="section"/>
                        </group>
                        <group string="Reporting Structure">
                            <field name="direct_report"/>
                            <field name="direct_supervise"/>
                            <field name="functional_report"/>
                            <field name="functional_supervise"/>
                            <field name="direct_count"/>
                            <field name="indirect_count"/>
                            <field name="total_supervised" readonly="1"/>
                        </group>
                        <group string="Responsibilities">
                            <field name="core_responsibilities"/>
                            <field name="functional_responsibilities"/>
                        </group>
                        <group string="Specifications">
                            <field name="qualifications"/>
                            <field name="experience"/>
                            <field name="skills"/>
                        </group>
                        <group string="Authority Limits">
                            <field name="authority_limit_staff"/>
                            <field name="contractual_single"/>
                            <field name="contractual_joint"/>
                            <field name="petty_cash"/>
                            <field name="revenue_expenditure"/>
                            <field name="capital_expenditure"/>
                        </group>
                        <field name="state" readonly="1"/>
                        <footer>
                            <button name="action_submit" type="object" string="Submit" attrs="{'invisible': [('state','!=','draft')]}"/>
                            <button name="action_approve" type="object" string="Approve" groups="base.group_system" attrs="{'invisible': [('state','!=','submitted')]}"/>
                            <button name="action_reject" type="object" string="Reject" groups="base.group_system" attrs="{'invisible': [('state','!=','submitted')]}"/>
                            <button string="Print PDF" type="report" name="job_description_module.report_job_description_document" attrs="{'invisible': [('state','!=','approved')]}"/>
                        </footer>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_tree_job_description" model="ir.ui.view">
            <field name="name">job.description.tree</field>
            <field name="model">job.description</field>
            <field name="arch" type="xml">
                <tree>
                    <field name="name"/>
                    <field name="company_id"/>
                    <field name="department_id"/>
                    <field name="job_grade"/>
                    <field name="state"/>
                </tree>
            </field>
        </record>

        <record id="action_job_description" model="ir.actions.act_window">
            <field name="name">Job Descriptions</field>
            <field name="res_model">job.description</field>
            <field name="view_mode">tree,form</field>
        </record>

        <menuitem id="menu_job_description_root" name="Job Descriptions" sequence="10"/>
        <menuitem id="menu_job_description" name="Manage Descriptions" parent="menu_job_description_root" action="action_job_description"/>
    </data>
</odoo>
