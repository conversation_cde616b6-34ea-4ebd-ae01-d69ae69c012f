<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- General Management Tree View -->
        <record id="view_general_management_tree" model="ir.ui.view">
            <field name="name">general.management.tree</field>
            <field name="model">general.management</field>
            <field name="arch" type="xml">
                <tree string="General Managements" default_order="country_id, sequence, name">
                    <field name="sequence" widget="handle"/>
                    <field name="country_id"/>
                    <field name="name"/>
                    <field name="code"/>
                    <field name="manager_name"/>
                    <field name="phone"/>
                    <field name="companies_count" string="Companies"/>
                    <field name="branches_count" string="Branches"/>
                    <field name="active"/>
                </tree>
            </field>
        </record>

        <!-- General Management Form View -->
        <record id="view_general_management_form" model="ir.ui.view">
            <field name="name">general.management.form</field>
            <field name="model">general.management</field>
            <field name="arch" type="xml">
                <form string="General Management">
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                                <field name="active" widget="boolean_button" options='{"terminology": "archive"}'/>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <label for="name" class="oe_edit_only"/>
                            <h1>
                                <field name="name" placeholder="General Management Name..."/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="country_id" options="{'no_create': True}"/>
                                <field name="code" placeholder="e.g., GM001"/>
                                <field name="sequence"/>
                            </group>
                            <group>
                                <field name="manager_name" placeholder="Manager Full Name"/>
                                <field name="phone" widget="phone"/>
                                <field name="email" widget="email"/>
                                <field name="companies_count" readonly="1"/>
                                <field name="branches_count" readonly="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Address &amp; Contact" name="contact">
                                <group>
                                    <field name="address" placeholder="Full address of the general management..."/>
                                </group>
                            </page>
                            
                            <page string="Description" name="description">
                                <field name="description" placeholder="Additional information about the general management..."/>
                            </page>
                            
                            <page string="Companies" name="companies">
                                <field name="company_ids" readonly="1">
                                    <tree>
                                        <field name="name"/>
                                        <field name="company_branch_id"/>
                                        <field name="phone"/>
                                        <field name="email"/>
                                    </tree>
                                </field>
                            </page>
                            
                            <page string="Branches" name="branches">
                                <field name="branch_ids" readonly="1">
                                    <tree>
                                        <field name="name"/>
                                        <field name="code"/>
                                        <field name="branch_type"/>
                                        <field name="manager_name"/>
                                        <field name="phone"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- General Management Search View -->
        <record id="view_general_management_search" model="ir.ui.view">
            <field name="name">general.management.search</field>
            <field name="model">general.management</field>
            <field name="arch" type="xml">
                <search string="Search General Managements">
                    <field name="name" string="General Management"/>
                    <field name="code" string="Code"/>
                    <field name="country_id" string="Country"/>
                    <field name="manager_name" string="Manager"/>
                    <field name="phone"/>
                    <field name="email"/>
                    
                    <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>

                    <separator/>
                    <filter string="Has Companies" name="has_companies"
                            domain="[('company_ids', '!=', False)]"/>
                    <filter string="No Companies" name="no_companies"
                            domain="[('company_ids', '=', False)]"/>
                            
                    <separator/>
                    <filter string="Has Branches" name="has_branches"
                            domain="[('branch_ids', '!=', False)]"/>
                    <filter string="No Branches" name="no_branches"
                            domain="[('branch_ids', '=', False)]"/>
                    
                    <group expand="0" string="Group By">
                        <filter string="Country" name="group_by_country" 
                                context="{'group_by': 'country_id'}"/>
                        <filter string="Manager" name="group_by_manager" 
                                context="{'group_by': 'manager_name'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- General Management Action -->
        <record id="action_general_management" model="ir.actions.act_window">
            <field name="name">General Managements</field>
            <field name="res_model">general.management</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_general_management_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first general management!
                </p>
                <p>
                    General managements are organizational units within countries.
                    Each general management belongs to a specific country and can manage multiple companies.
                </p>
            </field>
        </record>
    </data>
</odoo>
