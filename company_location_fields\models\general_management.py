# -*- coding: utf-8 -*-

from odoo import models, fields, api


class GeneralManagement(models.Model):
    _name = 'general.management'
    _description = 'General Management'
    _order = 'country_id, sequence, name'
    _rec_name = 'name'

    name = fields.Char(
        string='General Management Name',
        required=True,
        translate=True,
        help='Name of the general management'
    )
    code = fields.Char(
        string='Code',
        size=10,
        help='Short code for the general management'
    )
    country_id = fields.Many2one(
        'company.country',
        string='Country',
        required=True,
        ondelete='cascade',
        help='Country this general management belongs to'
    )
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Used to order general managements'
    )
    active = fields.Boolean(
        string='Active',
        default=True,
        help='If unchecked, it will allow you to hide the general management without removing it.'
    )
    description = fields.Text(
        string='Description',
        translate=True,
        help='Additional information about the general management'
    )
    address = fields.Text(
        string='Address',
        translate=True,
        help='Address of the general management'
    )
    phone = fields.Char(
        string='Phone',
        help='Phone number of the general management'
    )
    email = fields.Char(
        string='Email',
        help='Email address of the general management'
    )
    manager_name = fields.Char(
        string='Manager Name',
        translate=True,
        help='Name of the general manager'
    )
    company_ids = fields.One2many(
        'res.company',
        'general_management_id',
        string='Companies',
        help='Companies under this general management'
    )
    
    branch_ids = fields.One2many(
        'company.branch',
        'general_management_id',
        string='Branches',
        help='Branches under this general management'
    )

    @api.depends('name', 'code', 'country_id')
    def name_get(self):
        result = []
        for record in self:
            if record.code:
                name = record.code
            else:
                name = record.name
            result.append((record.id, name))
        return result

    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        args = args or []
        if name:
            # Search by name, code, or country name
            domain = [
                '|', '|', 
                ('name', operator, name), 
                ('code', operator, name),
                ('country_id.name', operator, name)
            ]
            records = self.search(domain + args, limit=limit)
            return records.name_get()
        return super(GeneralManagement, self).name_search(name, args, operator, limit)

    @api.onchange('country_id')
    def _onchange_country_id(self):
        """Clear the field if country changes"""
        if self.country_id:
            return {'domain': {'country_id': [('active', '=', True)]}}
        return {}

    @api.depends('company_ids')
    def _compute_companies_count(self):
        for record in self:
            record.companies_count = len(record.company_ids)

    companies_count = fields.Integer(
        string='Companies Count',
        compute='_compute_companies_count',
        store=True,
        help='Number of companies under this general management'
    )
    
    @api.depends('branch_ids')
    def _compute_branches_count(self):
        for record in self:
            record.branches_count = len(record.branch_ids)

    branches_count = fields.Integer(
        string='Branches Count',
        compute='_compute_branches_count',
        store=True,
        help='Number of branches under this general management'
    )
