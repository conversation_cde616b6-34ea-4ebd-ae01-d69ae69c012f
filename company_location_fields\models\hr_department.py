# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class HrDepartment(models.Model):
    _inherit = 'hr.department'

    # Add company fields for reference
    company_id = fields.Many2one('res.company', string='Company', index=True, default=lambda self: self.env.company)
    
    sector_code = fields.Char(
        string='Sector Code',
        size=10,
        help='Code for the sector this department belongs to'
    )
    
    division_number = fields.Char(
        string='Division Number',
        size=10,
        help='Number of the division this department belongs to',
        copy=False  # Prevent copying this field when duplicating a record
    )
    
    department_number = fields.Char(
        string='Department Number',
        size=10,
        help='Number of this department',
        copy=False  # Prevent copying this field when duplicating a record
    )
    
    unit_number = fields.Char(
        string='Unit Number',
        size=10,
        help='Number of the unit this department belongs to',
        copy=False  # Prevent copying this field when duplicating a record
    )
    
    full_code = fields.Char(
        string='Full Code',
        compute='_compute_full_code',
        store=True,
        help='Combined code of all location and department fields'
    )
    
    @api.depends('company_id.company_country_id', 'company_id.general_management_id', 
               'company_id.company_branch_id', 'sector_code', 'division_number', 
               'department_number', 'unit_number')
    def _compute_full_code(self):
        """Compute the full code combining all location and department fields"""
        for department in self:
            parts = []
            
            # Add company country code if available
            if department.company_id and department.company_id.company_country_id:
                country_code = department.company_id.company_country_id.code or ''
                if country_code:
                    parts.append(country_code)
            
            # Add general management code if available
            if department.company_id and department.company_id.general_management_id:
                gm_code = department.company_id.general_management_id.code or ''
                if gm_code:
                    parts.append(gm_code)
            
            # Add company branch code if available
            if department.company_id and department.company_id.company_branch_id:
                branch_code = department.company_id.company_branch_id.code or ''
                if branch_code:
                    parts.append(branch_code)
            
            # Add sector code if available
            if department.sector_code:
                parts.append(department.sector_code)
                
            # Add division number if available
            if department.division_number:
                parts.append(department.division_number)
                
            # Add department number if available
            if department.department_number:
                parts.append(department.department_number)
                
            # Add unit number if available
            if department.unit_number:
                parts.append(department.unit_number)
            
            # Join all parts with hyphen
            department.full_code = '-'.join(parts) if parts else ''
    
    @api.constrains('division_number', 'department_number', 'unit_number')
    def _check_unique_numbers(self):
        """Check that division_number, department_number, and unit_number are unique"""
        for record in self:
            # Check division_number uniqueness
            if record.division_number:
                domain = [
                    ('division_number', '=', record.division_number),
                    ('id', '!=', record.id)
                ]
                if record.company_id:
                    domain.append(('company_id', '=', record.company_id.id))
                
                duplicate = self.search(domain, limit=1, order='create_date DESC')
                if duplicate:
                    last_value = duplicate.division_number
                    raise ValidationError(_(
                        "Division Number must be unique! The value '%s' is already used. "
                        "The last used value was '%s'. Please use a different value."
                    ) % (record.division_number, last_value))
            
            # Check department_number uniqueness
            if record.department_number:
                domain = [
                    ('department_number', '=', record.department_number),
                    ('id', '!=', record.id)
                ]
                if record.company_id:
                    domain.append(('company_id', '=', record.company_id.id))
                
                duplicate = self.search(domain, limit=1, order='create_date DESC')
                if duplicate:
                    last_value = duplicate.department_number
                    raise ValidationError(_(
                        "Department Number must be unique! The value '%s' is already used. "
                        "The last used value was '%s'. Please use a different value."
                    ) % (record.department_number, last_value))
            
            # Check unit_number uniqueness
            if record.unit_number:
                domain = [
                    ('unit_number', '=', record.unit_number),
                    ('id', '!=', record.id)
                ]
                if record.company_id:
                    domain.append(('company_id', '=', record.company_id.id))
                
                duplicate = self.search(domain, limit=1, order='create_date DESC')
                if duplicate:
                    last_value = duplicate.unit_number
                    raise ValidationError(_(
                        "Unit Number must be unique! The value '%s' is already used. "
                        "The last used value was '%s'. Please use a different value."
                    ) % (record.unit_number, last_value))
    
    @api.model
    def get_last_used_numbers(self, company_id=None):
        """Get the last used division, department, and unit numbers"""
        domain = []
        if company_id:
            domain.append(('company_id', '=', company_id))
        
        # Get last division number
        last_division = self.search(
            domain + [('division_number', '!=', False)], 
            limit=1, 
            order='create_date DESC, division_number DESC'
        )
        
        # Get last department number
        last_department = self.search(
            domain + [('department_number', '!=', False)], 
            limit=1, 
            order='create_date DESC, department_number DESC'
        )
        
        # Get last unit number
        last_unit = self.search(
            domain + [('unit_number', '!=', False)], 
            limit=1, 
            order='create_date DESC, unit_number DESC'
        )
        
        return {
            'division_number': last_division.division_number if last_division else False,
            'department_number': last_department.department_number if last_department else False,
            'unit_number': last_unit.unit_number if last_unit else False
        }
    
    # We're not overriding _compute_complete_name anymore to avoid affecting the parent_id field
    # This ensures that the hierarchical structure remains intact