from odoo import models, fields, api, _
from odoo.exceptions import UserError


class BSSICFreeEntryRequest(models.Model):
    """Free Entry Request Model"""
    _name = 'bssic.free.entry.request'
    _description = 'BSSIC Free Entry Request'
    _inherit = 'bssic.base.request'

    # Free entry specific fields are inherited from base_request.py
    # No need to redefine them here

    @api.constrains('free_entry_subject', 'free_entry_details', 'free_entry_from_date', 'free_entry_to_date',
                    'free_entry_user_name', 'free_entry_type', 'state', 'show_free_entry_fields')
    def _check_required_free_entry_fields(self):
        """Validate required fields for free entry requests"""
        for record in self:
            if record.show_free_entry_fields and record.state != 'draft':

                if not record.free_entry_subject:
                    raise UserError(_('Subject is required for Free Form requests.'))

                if not record.free_entry_details:
                    raise UserError(_('Operation Details are required for Free Form requests.'))

                if not record.free_entry_from_date:
                    raise UserError(_('From Date is required for Free Form requests.'))

                if not record.free_entry_to_date:
                    raise UserError(_('To Date is required for Free Form requests.'))

                if not record.free_entry_user_name:
                    raise UserError(_('User Name is required for Free Form requests.'))

                if not record.free_entry_type:
                    raise UserError(_('Entry Type is required for Free Form requests.'))

                if record.free_entry_from_date and record.free_entry_to_date:
                    if record.free_entry_to_date <= record.free_entry_from_date:
                        raise UserError(_('To Date must be after From Date in Free Form requests.'))

    @api.model
    def create(self, vals):
        """Override create to set free entry specific defaults"""
        # Set request type code for free entry
        if not vals.get('request_type_code'):
            vals['request_type_code'] = 'free_entry'

        # Find and set the free entry request type
        if not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'free_entry')
            ], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id
        
        # Set name to "Free Form Request" followed by sequence number
        if vals.get('name', _('New')) == _('New'):
            sequence = self.env['ir.sequence'].next_by_code('bssic.request') or _('New')
            vals['name'] = f"Free Form Request - {sequence}"

        return super(BSSICFreeEntryRequest, self).create(vals)

    @api.model
    def default_get(self, fields_list):
        """Set default values for free entry requests"""
        res = super(BSSICFreeEntryRequest, self).default_get(fields_list)

        # Set default request type for free entry
        if 'request_type_code' in fields_list:
            res['request_type_code'] = 'free_entry'

        if 'request_type_id' in fields_list:
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'free_entry')
            ], limit=1)
            if request_type:
                res['request_type_id'] = request_type.id

        return res
